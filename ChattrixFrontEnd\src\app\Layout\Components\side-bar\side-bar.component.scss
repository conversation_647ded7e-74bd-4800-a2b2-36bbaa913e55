/* Sidebar Container */
.sidebar-container {
  width: 280px;
  height: 100vh;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: var(--spacing-md);
  box-sizing: border-box;
}

/* User Profile Section */
.user-profile-section {
  margin-bottom: var(--spacing-lg);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) 0;
}

.app-logo {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.logo-fallback {
  width: 40px;
  height: 40px;
  background: #000000; /* Black background for minimalist design */
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
}

.app-name {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

/* User Info Card */
.user-info-card {
  background: var(--bg-card);
  // border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  // box-shadow: var(--shadow-sm);
  // border-bottom: 2px solid var(--border-primary);
}

.user-avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-md);
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  border: 3px solid var(--border-primary);
  overflow: hidden;

  &.has-image {
    border-color: #000000; /* Black border for minimalist design */
  }
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-initials {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.user-details {
  text-align: center;
}

.user-name {
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
}

.user-phone {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0 0 var(--spacing-md) 0;
  font-weight: 500;
}

.user-contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.contact-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
  text-align: left;
}

.contact-label {
  color: var(--text-muted);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.contact-value {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.4;
  word-break: break-word;
}

/* Navigation Section */
.navigation-section {
  border-top: 1px solid var(--border-secondary);
  flex: 1;
  margin-bottom: var(--spacing-lg);
}

.nav-list {
  padding: 0;
}

.nav-item {
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-xs);
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);

  &:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
  }

  &.active {
    background: #000000; /* Black background for active state */
    color: white;

    .nav-icon {
      color: white;
    }
  }

  .nav-icon {
    color: var(--text-secondary);
    margin-right: var(--spacing-sm);
    transition: color 0.2s ease;
  }

  .nav-label {
    // color: white;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95rem;
  }

  .nav-badge {
    background: #000000; /* Black background for badges */
    color: white;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
  }
}

/* Settings Section */
.settings-section {
  margin-top: auto;
  padding-top: var(--spacing-md);
}

.setting-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) 0;
  margin-bottom: var(--spacing-sm);
}

.setting-icon {
  color: var(--text-secondary);
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.setting-label {
  flex: 1;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.95rem;
}

.theme-toggle {
  ::ng-deep .mat-slide-toggle-bar {
    background-color: var(--bg-tertiary);
  }

  ::ng-deep .mat-slide-toggle-thumb {
    background-color: var(--text-secondary);
  }

  &.mat-checked {
    ::ng-deep .mat-slide-toggle-bar {
      background-color: #000000; /* Black background when checked */
    }

    ::ng-deep .mat-slide-toggle-thumb {
      background-color: white;
    }
  }
}

.logout-button {
  width: 100%;
  margin-top: var(--spacing-md);
  border: 1px solid var(--error);
  color: var(--error);
  background: transparent;
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    background: var(--error);
    color: white;
  }

  mat-icon {
    margin-right: var(--spacing-xs);
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar-container {
    width: 100%;
    height: auto;
    min-height: 100vh;
  }

  .user-info-card {
    margin-bottom: var(--spacing-md);
  }

  .user-avatar {
    width: 60px;
    height: 60px;
  }

  .app-name {
    font-size: 1.25rem;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .sidebar-container {
    background: #ffffff;
    border-right-color: #e0e0e0;
  }

  .user-info-card {
    background: #ffffff;
    border-color: #e0e0e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .nav-item {
    color: #666666;

    &:hover {
      background: #f5f5f5;
      color: #333333;
    }

    &.active {
      background: var(--accent-green);
      color: white;
    }

    .nav-icon {
      color: #666666;
    }
  }

  .setting-icon {
    color: #666666;
  }

  .setting-label {
    color: #333333;
  }

  .app-name {
    color: #333333;
  }

  .user-name {
    color: #333333;
  }

  .user-phone {
    color: #666666;
  }

  .contact-label {
    color: #999999;
  }

  .contact-value {
    color: #666666;
  }

  .avatar-initials {
    color: #333333;
  }
}
