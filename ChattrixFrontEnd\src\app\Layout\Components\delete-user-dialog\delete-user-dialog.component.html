<div class="delete-user-dialog">
  <!-- <PERSON>alog Header -->
  <div class="dialog-header">
    <h2 mat-dialog-title class="dialog-title">
      <mat-icon [class]="warningClass">{{ warningIcon }}</mat-icon>
      Delete User
    </h2>
  </div>

  <!-- Dialog Content -->
  <div mat-dialog-content class="dialog-content">
    <!-- User Information -->
    <div class="user-info-section">
      <div class="user-summary">
        <div class="user-avatar" *ngIf="data.user.profileImageUrl">
          <img [src]="data.user.profileImageUrl" [alt]="getUserDisplayName()" />
        </div>
        <div class="user-avatar-placeholder" *ngIf="!data.user.profileImageUrl">
          <span>{{ getUserDisplayName().charAt(0) }}</span>
        </div>
        <div class="user-details">
          <h3 class="user-name">{{ getUserDisplayName() }}</h3>
          <p class="user-email">{{ data.user.email }}</p>
          <p class="user-roles">{{ getUserRoleDisplay() }}</p>
        </div>
      </div>
    </div>

    <!-- Warning Message -->
    <div class="warning-section">
      <mat-card [class]="'warning-card ' + warningClass">
        <mat-card-content>
          <div class="warning-content">
            <mat-icon [class]="warningClass">{{ warningIcon }}</mat-icon>
            <div class="warning-text">
              <h4>
                {{ canDelete ? "Confirm Deletion" : "Action Not Permitted" }}
              </h4>
              <p>{{ warningMessage }}</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Reason Input (only if user can delete) -->
    <div class="reason-section" *ngIf="canDelete">
      <mat-form-field appearance="outline" class="reason-field">
        <mat-label>Reason for deletion (required)</mat-label>
        <textarea
          matInput
          [formControl]="reasonControl"
          placeholder="Please provide a detailed reason for deleting this user..."
          rows="4"
          maxlength="500"
        ></textarea>
        <mat-hint align="end"
          >{{ reasonControl.value?.length || 0 }}/500</mat-hint
        >
        <mat-error *ngIf="reasonControl.hasError('required')">
          Reason is required
        </mat-error>
        <mat-error *ngIf="reasonControl.hasError('minlength')">
          Reason must be at least 10 characters long
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Additional Warnings for High-Privilege Users -->
    <div class="additional-warnings" *ngIf="canDelete">
      <div class="warning-item" *ngIf="hasAdminPrivileges()">
        <mat-icon class="warning-icon">info</mat-icon>
        <span
          >This user has administrative privileges that will be lost
          permanently.</span
        >
      </div>
      <div class="warning-item">
        <mat-icon class="warning-icon">info</mat-icon>
        <span
          >All user data, including chat history and files, will be permanently
          deleted.</span
        >
      </div>
      <div class="warning-item">
        <mat-icon class="warning-icon">info</mat-icon>
        <span
          >This action cannot be undone and the user cannot be recovered.</span
        >
      </div>
    </div>
  </div>

  <!-- Dialog Actions -->
  <div mat-dialog-actions class="dialog-actions">
    <button mat-button (click)="onCancel()" class="cancel-button">
      Cancel
    </button>
    <button
      mat-raised-button
      color="warn"
      (click)="onConfirm()"
      [disabled]="!canDelete || !reasonControl.valid || isDeleting"
      class="delete-button"
      *ngIf="canDelete"
    >
      <mat-icon>delete_forever</mat-icon>
      {{ isDeleting ? "Deleting..." : "Delete User" }}
    </button>
  </div>
</div>
