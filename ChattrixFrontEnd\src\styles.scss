/* Chattrix Global Styles with Material UI Integration */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=SF+Pro+Text:wght@400;500;600;700&display=swap");
@import "@angular/material/prebuilt-themes/indigo-pink.css";

/* CSS Variables for Chattrix Color Palette */
:root {
  /* Primary Colors - Based on reference image */
  --primary-dark: #0f172a;
  --primary-darker: #020617;
  --secondary-dark: #1e293b;
  --accent-green: #10b981;
  --accent-green-hover: #059669;
  --accent-green-light: #34d399;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #94a3b8;
  --text-muted: #64748b;
  --text-disabled: #475569;

  /* Background Colors */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-card: #1e293b;
  --bg-input: #334155;
  --bg-hover: #475569;

  /* Border Colors */
  --border-primary: #334155;
  --border-secondary: #475569;
  --border-focus: #10b981;

  /* Status Colors */
  --success: #10b981;
  --error: #ef4444;
  --warning: #f59e0b;
  --info: #3b82f6;

  /* Material UI Overrides */
  --mdc-theme-primary: #10b981;
  --mdc-theme-secondary: #34d399;
  --mdc-theme-surface: #1e293b;
  --mdc-theme-background: #0f172a;
  --mdc-theme-on-primary: #ffffff;
  --mdc-theme-on-secondary: #000000;
  --mdc-theme-on-surface: #ffffff;
  --mdc-theme-on-background: #ffffff;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md:
    0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg:
    0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl:
    0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Typography */
  --font-family:
    "Inter SemiBold", "SF Pro Display Medium", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Global Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Typography Classes */
.text-primary {
  color: var(--text-primary);
}
.text-secondary {
  color: var(--text-secondary);
}
.text-muted {
  color: var(--text-muted);
}
.text-disabled {
  color: var(--text-disabled);
}
.text-success {
  color: var(--success);
}
.text-error {
  color: var(--error);
}
.text-warning {
  color: var(--warning);
}
.text-info {
  color: var(--info);
}

/* Font Weight Classes */
.font-light {
  font-weight: 300;
}
.font-normal {
  font-weight: 400;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}

/* Font Size Classes */
.text-xs {
  font-size: var(--font-size-xs);
}
.text-sm {
  font-size: var(--font-size-sm);
}
.text-base {
  font-size: var(--font-size-base);
}
.text-lg {
  font-size: var(--font-size-lg);
}
.text-xl {
  font-size: var(--font-size-xl);
}
.text-2xl {
  font-size: var(--font-size-2xl);
}
.text-3xl {
  font-size: var(--font-size-3xl);
}
.text-4xl {
  font-size: var(--font-size-4xl);
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__trailing {
  border-color: var(--border-primary) !important;
}

/* Change the border hover color */
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
    .mdc-text-field--focused
  ):hover
  .mdc-notched-outline
  .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
    .mdc-text-field--focused
  ):hover
  .mdc-notched-outline
  .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
    .mdc-text-field--focused
  ):hover
  .mdc-notched-outline
  .mdc-notched-outline__trailing {
  border-color: var(--border-secondary) !important;
}

/* Change the border focused color */
.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused
  .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused
  .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused
  .mdc-notched-outline__trailing {
  border-color: var(--border-focus) !important;
}

.mat-mdc-paginator-navigation-next,
.mat-mdc-paginator-navigation-previous,
.mat-mdc-paginator-navigation-first,
.mat-mdc-paginator-navigation-last {
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  background: var(--bg-secondary);
  width: 36px;
  height: 36px;
  margin: 0 var(--spacing-xs);

  &:hover:not([disabled]) {
    background: var(--bg-hover);
    color: var(--text-primary);
    border-color: var(--border-secondary);
  }

  &[disabled] {
    color: var(--text-muted);
    background: var(--bg-tertiary);
    border-color: var(--border-primary);
    opacity: 0.6;
    cursor: not-allowed;
  }

  .mat-mdc-button-touch-target {
    width: 36px;
    height: 36px;
  }

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

/* Material UI Component Overrides for Dark Theme */
.mat-mdc-form-field {
  --mdc-filled-text-field-container-color: var(--bg-input);
  --mdc-filled-text-field-label-text-color: var(--text-secondary);
  --mdc-filled-text-field-input-text-color: var(--text-primary);
  --mdc-filled-text-field-active-indicator-color: var(--accent-green);
  --mdc-filled-text-field-focus-active-indicator-color: var(--accent-green);
  --mdc-filled-text-field-hover-active-indicator-color: var(
    --accent-green-light
  );

  /* Reduce form field height and spacing */
  .mdc-text-field {
    height: 48px;
  }

  .mat-mdc-form-field-wrapper {
    padding-bottom: 0;
  }

  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 4px;
    min-height: 16px;
  }
}

.mat-mdc-button.mat-primary {
  --mdc-filled-button-container-color: var(--accent-green);
  --mdc-filled-button-label-text-color: var(--text-primary);
}

.mat-mdc-button.mat-primary:hover {
  --mdc-filled-button-container-color: var(--accent-green-hover);
}

.mat-mdc-card {
  border-radius: 10px;
  --mdc-elevated-card-container-color: var(--bg-card);
  --mdc-elevated-card-container-shadow: var(--shadow-lg);
}

/* Authentication Layout Styles */
.auth-container {
  min-height: 100vh;
  min-height: 100dvh; /* Dynamic viewport height for mobile browsers */
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--primary-darker) 0%,
    var(--primary-dark) 50%,
    var(--secondary-dark) 100%
  );
  padding: var(--spacing-sm);
  box-sizing: border-box;
}

mat-card.auth-card {
  border-radius: 20px;
}

.auth-card {
  width: 100%;
  max-width: 400px;
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-primary);
  overflow: hidden;
}

.auth-header {
  text-align: center;
  padding: var(--spacing-md) var(--spacing-lg) var(--spacing-sm);
  background: linear-gradient(
    135deg,
    var(--bg-card) 0%,
    var(--bg-secondary) 100%
  );
}

.auth-logo {
  width: 110px;
  height: 110px;
  margin: 0 auto var(--spacing-md);
  border-radius: 50%;
  overflow: hidden;
  // border: 3px solid var(--accent-green);
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  margin-bottom: 20px;
}

.auth-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.auth-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: var(--line-height-tight);
}

.auth-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: 0;
  line-height: var(--line-height-normal);
}

.auth-form {
  padding: var(--spacing-md) var(--spacing-lg);
}

.auth-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: center;
  border-top: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

/* Mobile-First Responsive Design Enhancements */
@media (max-width: 768px) {
  .auth-container {
    padding: var(--spacing-sm);
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }

  .auth-card {
    margin: 0;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
  }

  .auth-header {
    padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-md);
  }

  .auth-form {
    padding: var(--spacing-lg);
  }

  .auth-footer {
    padding: var(--spacing-md) var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .auth-container {
    padding: var(--spacing-xs);
  }

  .auth-card {
    border-radius: var(--radius-md);
  }

  .auth-header {
    padding: var(--spacing-lg) var(--spacing-md) var(--spacing-sm);
  }

  .auth-form {
    padding: var(--spacing-md);
  }

  .auth-footer {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .auth-title {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-tight);
  }

  .auth-subtitle {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
  }
}

/* Touch-Friendly Interactions */
@media (hover: none) and (pointer: coarse) {
  /* Increase touch targets for mobile */
  .mat-mdc-button {
    min-height: 44px;
    min-width: 44px;
  }

  .mat-mdc-icon-button {
    width: 44px;
    height: 44px;
  }

  /* Remove hover effects on touch devices */
  .mat-mdc-button:hover {
    background-color: inherit;
  }

  /* Improve form field touch targets */
  .mat-mdc-form-field .mat-mdc-text-field-wrapper {
    min-height: 48px;
  }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .auth-logo img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Landscape Orientation on Mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .auth-container {
    padding: var(--spacing-xs);
  }

  .auth-card {
    max-height: 90vh;
    overflow-y: auto;
  }

  .auth-header {
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-sm);
  }

  .auth-logo {
    width: 50px;
    height: 50px;
    margin-bottom: var(--spacing-sm);
  }

  .auth-title {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
  }

  .auth-subtitle {
    font-size: var(--font-size-xs);
    margin-bottom: var(--spacing-xs);
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark Mode Support (if system preference changes) */
@media (prefers-color-scheme: dark) {
  /* Our app is already dark-themed, but we can add specific adjustments here if needed */
  :root {
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md:
      0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg:
      0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --shadow-xl:
      0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  }
}
