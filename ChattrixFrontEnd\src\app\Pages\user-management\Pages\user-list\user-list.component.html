<div class="user-management-container">
  <!-- Header Section -->
  <div class="header-section">
    <div class="header-content">
      <h1 class="page-title">User Management</h1>
      <p class="page-subtitle">Manage users, roles, and permissions</p>
    </div>
    <div class="header-actions">
      <button
        mat-raised-button
        color="primary"
        class="add-user-btn"
        (click)="onAddUser()"
        *ngIf="hasAdminAccess"
      >
        <mat-icon>person_add</mat-icon>
        Add User
      </button>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="filters-section">
    <mat-card class="filters-card">
      <div class="filters-content">
        <!-- Search Input -->
        <mat-form-field appearance="outline" class="search-field">
          <mat-label [ngStyle]="{ color: 'var(--text-secondary)' }"
            >Search users</mat-label
          >
          <input
            matInput
            [formControl]="searchControl"
            [ngStyle]="{ color: 'var(--text-secondary)' }"
            placeholder="Search by name or email"
          />
          <mat-icon matSuffix [ngStyle]="{ color: 'var(--text-secondary)' }"
            >search</mat-icon
          >
        </mat-form-field>

        <!-- Status Filter -->
        <mat-form-field appearance="outline" class="filter-field">
          <mat-label [ngStyle]="{ color: 'var(--text-secondary)' }"
            >Status</mat-label
          >
          <mat-select
            [ngStyle]="{ color: 'var(--text-secondary)' }"
            [formControl]="statusFilter"
          >
            <mat-option value="all">All</mat-option>
            <mat-option value="active">Active</mat-option>
            <mat-option value="inactive">Inactive</mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Role Filter -->
        <mat-form-field appearance="outline" class="filter-field">
          <mat-label [ngStyle]="{ color: 'var(--text-secondary)' }"
            >Role</mat-label
          >
          <mat-select
            [ngStyle]="{ color: 'var(--text-secondary)' }"
            [formControl]="roleFilter"
          >
            <mat-option value="">All Roles</mat-option>
            <mat-option value="admin">Admin</mat-option>
            <mat-option value="user">User</mat-option>
            <mat-option value="super admin">Super Admin</mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Refresh Button -->
        <button
          mat-icon-button
          class="refresh-btn"
          (click)="refreshUsers()"
          matTooltip="Refresh"
        >
          <mat-icon>refresh</mat-icon>
        </button>
      </div>
    </mat-card>
  </div>

  <!-- Table Section -->
  <div class="table-section">
    <mat-card class="table-card">
      <!-- Loading Indicator -->
      <div *ngIf="loadingStates.fetchingUsers" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p class="loading-text">Loading users...</p>
      </div>

      <!-- Table Content -->
      <div *ngIf="!loadingStates.fetchingUsers" class="table-container">
        <table
          mat-table
          [dataSource]="dataSource"
          matSort
          (matSortChange)="onSortChange($event)"
          class="users-table"
        >
          <!-- Full Name Column -->
          <ng-container matColumnDef="fullName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Full Name</th>
            <td mat-cell *matCellDef="let user">
              <div class="user-info">
                <div class="user-avatar" *ngIf="user.profileImageUrl">
                  <img [src]="user.profileImageUrl" [alt]="user.fullName" />
                </div>
                <div
                  class="user-avatar-placeholder"
                  *ngIf="!user.profileImageUrl"
                >
                  <span>{{ user.fullName?.charAt(0) || "U" }}</span>
                </div>
                <div class="user-details">
                  <span class="user-name">{{ user.fullName || "N/A" }}</span>
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Email Column -->
          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
            <td mat-cell *matCellDef="let user">
              <span class="user-email">{{ user.email || "N/A" }}</span>
            </td>
          </ng-container>

          <!-- Roles Column -->
          <ng-container matColumnDef="roles">
            <th mat-header-cell *matHeaderCellDef>Roles</th>
            <td mat-cell *matCellDef="let user">
              <div class="roles-container">
                <mat-chip-set>
                  <mat-chip
                    *ngFor="let role of user.roles"
                    [class]="
                      'role-chip role-' + role.toLowerCase().replace(' ', '-')
                    "
                  >
                    {{ role }}
                  </mat-chip>
                </mat-chip-set>
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="isActive">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
            <td mat-cell *matCellDef="let user">
              <mat-chip
                [class]="user.isActive ? 'status-active' : 'status-inactive'"
              >
                <mat-icon>{{
                  user.isActive ? "check_circle" : "cancel"
                }}</mat-icon>
                {{ user.statusDisplay }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Created On Column -->
          <ng-container matColumnDef="createdOn">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Created On
            </th>
            <td mat-cell *matCellDef="let user">
              <span class="created-date">{{ user.formattedCreatedOn }}</span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let user">
              <div class="actions-container">
                <button
                  mat-icon-button
                  *ngIf="user.actions.canView"
                  (click)="onViewUser(user)"
                  matTooltip="View Details"
                  class="action-btn view-btn"
                >
                  <mat-icon>visibility</mat-icon>
                </button>
                <button
                  mat-icon-button
                  *ngIf="user.actions.canEdit"
                  (click)="onEditUser(user)"
                  matTooltip="Edit User"
                  class="action-btn edit-btn"
                >
                  <mat-icon>edit</mat-icon>
                </button>
                <button
                  mat-icon-button
                  *ngIf="user.actions.canDelete"
                  (click)="onDeleteUser(user)"
                  matTooltip="Delete User"
                  class="action-btn delete-btn"
                >
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>

        <!-- Empty State -->
        <div *ngIf="dataSource.data.length === 0" class="empty-state">
          <mat-icon class="empty-icon">people_outline</mat-icon>
          <h3>No users found</h3>
          <p>No users match your current filters.</p>
        </div>
      </div>

      <!-- Pagination -->
      <mat-paginator
        *ngIf="!loadingStates.fetchingUsers && dataSource.data.length > 0"
        [length]="totalItems"
        [pageSize]="pageSize"
        [pageSizeOptions]="pageSizeOptions"
        [pageIndex]="pageIndex"
        (page)="onPageChange($event)"
        showFirstLastButtons
        class="mat-mdc-paginator"
      ></mat-paginator>
    </mat-card>
  </div>
</div>
